<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="UTF-8">
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0"
        >
        <meta
            http-equiv="X-UA-Compatible"
            content="ie=edge"
        >
        <title>فاکتور - {{ $order?->fullname }} - {{ $order?->phone }} - کدسفارش - {{ $order?->code }} -</title>
        <style>
            @font-face {
                font-family: 'IRANSansWeb';
                src: url('/../assets/fonts/IRANSansWeb.eot');
                /* IE */
                src: url('/../assets/fonts/IRANSansWeb.eot?#iefix') format('embedded-opentype'),
                    /* IE */
                    url('/../assets/fonts/IRANSansWeb.woff') format('woff'),
                    /* Modern Browsers */
                    url('/../assets/fonts/IRANSansWeb.ttf') format('truetype');
                /* Safari, Android, iOS */
                font-weight: normal;
            }

            body {
                direction: rtl;
                font-family: "IRANSansWeb", sans-serif !important;
            }

            /* @page {
    size: a5;
} */
            .invoice {
                width: 100%;
                max-width: 1080px;
                margin: 0 auto;
                border: 1px solid #303030;
            }

            .header {
                text-align: center;
            }

            .items {
                width: 100%;
                border-collapse: collapse;
            }

            .items th,
            .items td {
                border: 1px solid #303030;
                padding: 8px;
                text-align: center;
            }

            .total {
                border-top: 1px solid #303030;
                padding-top: 10px;
            }

            .total>div {
                text-align: right;
            }
        </style>
    </head>

    <body>
        <div class="invoice">
            <div style="display: flex;width: 100%">
                <div style=" border: 0px solid #303030;border-left: 2px solid #303030;width: 80%">
                    <div
                        class="header"
                        style="background-color: rgb(240, 240, 240); padding:2px;"
                    >
                        <h1 style="font-size: 14px;font-weight: 800;padding:0px !important;margin:5px !important">مشخصات
                            فروشنده</h1>
                    </div>
                    <div>
                        <table class="items">
                            <thead>
                                <tr>
                                    <th style=" border: 0px solid #303030;">
                                        <p
                                            style="font-size: 13px;font-weight: 800;padding:0px !important;margin:3px !important">
                                            فروشگاه تیدامد</p>
                                    </th>
                                    <th
                                        style=" border: 0px solid #303030;"
                                        colspan="3"
                                    >
                                        <p
                                            style="font-size: 13px;font-weight: 500;padding:0px !important;margin:3px !important">
                                            آدرس :جنت آباد جنوبی، نرسیده به چهارراه لاله، پاساژ الماس، طبقه زیر همکف،
                                            واحد6</p>
                                    </th>
                                    <th style=" border: 0px solid #303030;;text-align:right">
                                        <p
                                            style="font-size: 13px;font-weight: 800;padding:0px !important;margin:3px !important">
                                            شماره تماس: 02171053350</p>
                                    </th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div
                        class="header"
                        style="background-color: rgb(240, 240, 240); padding:2px; border-top: 1px solid #303030"
                    >
                        <h1 style="font-size: 14px;font-weight: 800;padding:0px !important;margin:5px !important">مشخصات
                            خریدار</h1>
                    </div>
                    <div>
                        <table class="items">
                            <thead>
                                <tr>
                                    <th
                                        style=" border: 0px solid #303030;text-align:right;    font-weight: 500;"
                                        colspan="2"
                                    >
                                        <p style="font-size: 14px;padding:0px !important;margin:3px !important;">
                                            <span>نام و نام خانوادگی:
                                            </span>
                                            {{ $order?->resiver?->fullname ?? ($order?->factorTotal?->fullname_r ?? $order->fullname) }}

                                        </p>
                                    </th>
                                    <th
                                        style=" border: 0px solid #303030;"
                                        colspan="3"
                                    >
                                        <p
                                            style="font-size: 14px;font-weight: 500;padding:0px !important;margin:3px !important;">
                                            شماره تماس:
                                            {{ $order?->resiver?->mobile ?? ($order?->factorTotal?->phone_r ?? $order->phone) }}
                                        </p>
                                    </th>
                                    <th style=" border: 0px solid #303030;text-align:right">
                                        <p
                                            style="font-size: 14px;font-weight: 500;padding:0px !important;margin:3px !important;">
                                            کدمشتری: {{ $order?->subscribe?->code }}</p>
                                    </th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>

                </div>
                <div style="overflow: hidden;width: 20%">
                    <div
                        class="header"
                        style="background-color: rgb(240, 240, 240); padding:2px;width:100%"
                    >
                        <h1 style="font-size: 14px;font-weight: 800;padding:0px !important;margin:5px !important">مشخصات
                            فاکتور</h1>
                    </div>
                    {{-- @dd($order->factorTotal) --}}
                    <div style="padding: 10px;display: flex;flex-flow: column;gap: 10px;">
                        <div style=" border-bottom: 1px solid #303030;">
                            <p style="font-size: 14px;font-weight: 500;padding:0px !important;margin:3px !important">
                                شماره فاکتور: {{ $order->factorTotal->factor_number }}</p>
                        </div>
                        <div style=" border-bottom: 1px solid #303030;">
                            <p style="font-size: 14px;font-weight: 500;padding:0px !important;margin:3px !important">
                                تاریخ فاکتور: {{ $order->factorTotal->factor_create_date }}</p>
                        </div>
                        <div style="">
                            <p style="font-size: 14px;font-weight: 500;padding:0px !important;margin:3px !important">
                                قیمت روز طلا:
                                @php
                                    $gold18k =
                                        $order->gold18k != null
                                            ? $order->gold18k
                                            : $order->factor->pluck('gold18k')->first();
                                @endphp
                                {{ formatMoney($gold18k) }} <span style="font-size: 12px;color: #868686">(ریال)</span>
                            </p>
                        </div>
                    </div>

                </div>
            </div>

            <table class="items">
                <thead>
                    <tr>
                        <th>
                            <p style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">
                                ردیف</p>
                        </th>

                        <th>
                            <p style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">کد
                                سفارش</p>
                        </th>
                        <th>
                            <p style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">
                                اتیکت</p>
                        </th>
                        <th colspan="4">
                            <p style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">نام
                                کالا</p>
                        </th>
                        <th>
                            <p style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">
                                عیار</p>
                        </th>
                        <th>
                            <p style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">وزن
                            </p>
                        </th>
                        <th colspan="2">
                            <p style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">جمع
                                کل (ریال)</p>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @php $weight = 0 @endphp
                    @foreach ($order->factor as $key => $item)
                        {{-- @dd($weight, $item->weight) --}}
                        @php $weight += floatval($item->weight); @endphp

                        @if ($item->total != 0 && $item->total != '' && $item->total != null && $item->total != '')
                            <tr>
                                <th>
                                    <p
                                        style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">
                                        {{ $key + 1 }}</p>
                                </th>

                                <th>
                                    <p
                                        style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">
                                        {{ $item->code }}</p>
                                </th>
                                <th>
                                    <p
                                        style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">
                                        {{ $item->eticket }}</p>
                                </th>
                                <th colspan="4">
                                    <p
                                        style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">
                                        {{ $item->title }}</p>
                                </th>
                                <th>
                                    <p
                                        style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">
                                        {{ $item->cutie }}</p>
                                </th>
                                <th>
                                    <p
                                        style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">
                                        {{ $item->weight }}</p>
                                </th>
                                <th colspan="2">
                                    <p
                                        style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">
                                        {{ $item->total }}</p>
                                </th>
                            </tr>
                        @endif
                    @endforeach
                    <tr>
                        <th colspan="8">
                            <div style="display: flex; align-items: start;">
                                <p
                                    style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">
                                    {{ $order?->factorTotal?->description }}</p>
                            </div>

                        </th>
                        <th>
                            <p style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">
                                {{ $weight }}</p>
                        </th>
                        <th>
                            <div
                                style="padding-top: 5px;padding-bottom: 5px; border-bottom: 1px solid #303030;text-align: left;">
                                <p
                                    style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">
                                    جمع کل فاکتور <span style="font-size: 12px;color: #868686">(ریال)</span></p>
                            </div>
                            <div
                                style="padding-top: 5px;padding-bottom: 5px; border-bottom: 1px solid #303030;text-align: left;">
                                <p
                                    style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">
                                    بیعانه پرداخت شده <span style="font-size: 12px;color: #868686">(ریال)</span></p>
                            </div>
                            <div
                                style="padding-top: 5px;padding-bottom: 5px;; border-bottom: 1px solid #303030;text-align: left;">
                                <p
                                    style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">
                                    تخفیف <span style="font-size: 12px;color: #868686">(ریال)</span></p>
                            </div>
                            <div style="padding-top: 5px;text-align: left;">
                                <p
                                    style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">
                                    مبلغ قابل پرداخت <span style="font-size: 12px;color: #868686">(ریال)</span></p>
                            </div>
                        </th>
                        <th>
                            <div style="padding-top: 5px;padding-bottom: 5px; border-bottom: 1px solid #303030;">
                                <p
                                    style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">
                                    {{ $order->factorTotal->factor_total }}</p>
                            </div>
                            <div style="padding-top: 5px;padding-bottom: 5px; border-bottom: 1px solid #303030;">
                                <p
                                    style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">
                                    {{ $order->factorTotal->factor_deposit != null ? formatMoney($order->factorTotal->factor_deposit) : 0 }}
                                </p>
                            </div>
                            <div style="padding-top: 5px;padding-bottom: 5px;; border-bottom: 1px solid #303030;">
                                <p
                                    style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">
                                    {{ $order->factorTotal->factor_discount != null ? formatMoney($order->factorTotal->factor_discount) : 0 }}
                                </p>
                            </div>
                            <div
                                style="padding-top: 5px;"
                                dir="ltr"
                            >
                                @php
                                    $priceUniqe = str_replace([','], '', $order->factorTotal->total);
                                    $price = str_replace(['-', ','], '', $order->factorTotal->total);
                                    $price = (float) $price;
                                    $price = $price < 0 ? $price : $price;
                                @endphp
                                <p
                                    style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important; color : {{ $priceUniqe < 0 ? 'red' : 'green' }}">
                                    {{ $priceUniqe < 0 ? '- ' . formatMoney($price) : formatMoney($price) }}</p>
                            </div>
                        </th>
                    </tr>
                    <tr>
                        <th
                            style="text-align: right;"
                            colspan="11"
                        >
                            <p style="font-size: 16px;font-weight: 800;padding:0px !important;margin:3px !important">
                                مبلغ دریافتی با احتساب ارزش طلا، اجرت، سود، مالیات(اجرت+سود) و کسر تخفیفات</p>
                        </th>
                        {{-- <th>
                        <p style="font-size: 14px;font-weight: 800;padding:0px !important;margin:3px !important">{{ $order->factorTotal->total }}</p>
                    </th> --}}
                    </tr>
                </tbody>
            </table>
            <div
                class="header"
                style="padding:2px;text-align: right;"
            >
                <h1 style="font-size: 16px;font-weight: 800;padding:0px !important;margin:5px !important">مصنوع فروخته
                    شده بدون دلیل فنی و محکمه پسند به هیچ عنوان تعویض و یا پس گرفته نمیشود ولی اشتباه از طرفین قابل
                    برگشت و اصلاح میباشد. </h1>
            </div>
        </div>
        <script>
            document.addEventListener("DOMContentLoaded", () => {
                if (new URLSearchParams(window.location.search).get('auto') === 'true') {
                    window.print();
                }
            });
        </script>
    </body>

</html>
