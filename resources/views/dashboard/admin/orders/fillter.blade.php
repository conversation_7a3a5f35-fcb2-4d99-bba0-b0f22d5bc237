<div
    class="w-full overflow-hidden rounded-t-xl"
    x-data="{ fillter: false }"
>
    <div class="w-full overflow-hidden rounded-t-xl bg-gray-900 px-3 py-1.5">
        <div class="flex items-center justify-between">
            <div>
                <span class="flex items-center gap-2 text-base text-white">لیست سفارشات
                    <livewire:dashboard.orders.order-span :count="$count" />
                </span>
            </div>
            <div class="flex items-center gap-3">
                <div
                    class="flex items-center gap-4"
                    x-data="playerState()"
                    x-init="checkState()"
                >

                    <div x-show="isPlaying">
                        <span
                            class="text-sm text-gray-500"
                            x-text="countdown"
                        ></span>
                        <span class="text-sm text-gray-500">ثانیه</span>
                    </div>
                    <div
                        class="text-sm font-bold text-gray-100"
                        x-show="isPlaying"
                    >
                        زمان بروزرسانی </div>
                    <button
                        class="flex items-center gap-2 text-white"
                        @click="togglePlay"
                    >
                        <span :class="!isPlaying ? 'text-green-500' : 'text-gray-500'">
                            <svg
                                class="size-6"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M15.75 5.25v13.5m-7.5-13.5v13.5"
                                />
                            </svg>
                        </span>
                        <span :class="isPlaying ? 'text-green-500' : 'text-gray-500'">
                            <svg
                                class="size-6"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"
                                />
                            </svg>
                        </span>
                    </button>

                    <button
                        class="flex items-center gap-2 p-2"
                        @click="fillter = !fillter"
                    >
                        <svg
                            class="h-6 w-6 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75"
                            />
                        </svg>
                        <span class="text-base text-white">فیلتر</span>
                    </button>

                </div>

            </div>
        </div>

    </div>
    <div
        class="transform overflow-hidden bg-gray-800 px-6 transition-all"
        :class="fillter ? 'h-auto py-4' : 'h-0'"
        x-cloak
    >
        <div class="border-b border-gray-700 pb-3">
            <div class="mt-3 items-center gap-6 md:flex">
                <div class="flex items-center max-md:pb-3">
                    <input
                        class="peer hidden"
                        id="custom"
                        type="checkbox"
                        wire:model="data.filterDateBetweenChecked"
                        checked
                    />
                    <label
                        class="relative flex h-6 cursor-pointer select-none pr-8 text-sm text-slate-400 after:absolute after:right-0 after:flex after:h-6 after:w-6 after:items-center after:justify-center after:rounded after:border after:border-green-800 after:bg-white after:transition-[background-color] after:duration-300 after:ease-in after:content-[''] peer-checked:after:bg-green-300 peer-checked:after:font-bold peer-checked:after:text-green-800 peer-checked:after:transition-[background-color] peer-checked:after:duration-300 peer-checked:after:ease-in peer-checked:after:content-['x']"
                        for="custom"
                    >
                        فیلتر براساس بازده زمانی
                    </label>
                </div>
                <div class="flex items-center gap-3">
                    <div>
                        <select
                            class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-1.5 text-right text-sm text-white outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600 md:px-8"
                            id="MonthlyOne"
                            style="padding-right: 4px;"
                            wire:model="data.MonthlyOne"
                        >
                            <option selected>--انتخاب کنید--</option>
                            <option value="1">فروردین</option>
                            <option value="2">اردیبهشت</option>
                            <option value="3">خرداد</option>
                            <option value="4">تیر</option>
                            <option value="5">مرداد</option>
                            <option value="6">شهریور</option>
                            <option value="7">مهر</option>
                            <option value="8">آبان</option>
                            <option value="9">آذر</option>
                            <option value="10">دی</option>
                            <option value="11">بهمن</option>
                            <option value="12">اسفند</option>
                        </select>
                    </div>
                    <div>
                        <select
                            class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-1.5 text-right text-sm text-white outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600 md:px-8"
                            id="MonthlyTow"
                            style="padding-right: 4px;"
                            wire:model="data.MonthlyTow"
                        >
                            <option selected>--انتخاب کنید--</option>
                            <option value="1">فروردین</option>
                            <option value="2">اردیبهشت</option>
                            <option value="3">خرداد</option>
                            <option value="4">تیر</option>
                            <option value="5">مرداد</option>
                            <option value="6">شهریور</option>
                            <option value="7">مهر</option>
                            <option value="8">آبان</option>
                            <option value="9">آذر</option>
                            <option value="10">دی</option>
                            <option value="11">بهمن</option>
                            <option value="12">اسفند</option>
                        </select>
                    </div>
                    <div>
                        <select
                            class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-1.5 text-right text-sm text-white outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600 md:px-8"
                            id="Yearly"
                            style="padding-right: 4px;"
                            wire:model="data.Yearly"
                        >
                            <option selected>--انتخاب کنید--</option>
                            <option>1396</option>
                            <option>1397</option>
                            <option>1398</option>
                            <option>1399</option>
                            <option>1400</option>
                            <option>1401</option>
                            <option>1402</option>
                            <option>1403</option>
                            <option>1404</option>
                            <option>1405</option>
                            <option>1406</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-3 grid grid-cols-1 gap-3 md:grid-cols-8">
            <div>
                <label
                    class="mb-2 block text-sm text-white"
                    for="priceOne"
                >از مبلغ:</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="priceOne"
                    type="tel"
                    onkeyup="javascript:this.value=Comma(this.value);"
                    wire:model="data.priceOne"
                >
            </div>
            <div>
                <label
                    class="mb-2 block text-sm text-white"
                    for="priceTwo"
                >تا مبلغ:</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="priceTwo"
                    type="tel"
                    onkeyup="javascript:this.value=Comma(this.value);"
                    wire:model="data.priceTwo"
                >
            </div>
            <div>
                <label
                    class="mb-2 block text-sm text-white"
                    for="code_order"
                >کدسفارش:</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="code_order"
                    type="text"
                    wire:model="data.code"
                >
            </div>
            <div>
                <label
                    class="mb-2 block text-sm text-white"
                    for="category"
                >دسته بندی:</label>
                <select
                    class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 px-6 py-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="category"
                    wire:model="data.category"
                >
                    <option value="">--دسته--</option>
                    <option value="jewelry">گردنبند</option>
                    <option value="bracelet">دستبند</option>
                    <option value="ring">انگشتر</option>
                    <option value="spoon">ست</option>
                    <option value="ankle_jewlery">پابند</option>
                    <option value="jewelery">گوشواره</option>
                    <option value="multi">چندسفارشه</option>
                </select>
            </div>
            <div>
                <label
                    class="mb-2 block text-sm text-white"
                    for="type_construction"
                >نوع کار:</label>
                <select
                    class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 px-6 py-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="type_construction"
                    wire:model="data.type_construction"
                >
                    <option selected>--دسته--</option>
                    <option>آماده</option>
                    <option>سفارشی</option>
                </select>
            </div>
            <div>
                <label
                    class="mb-2 block text-sm text-white"
                    for="sex"
                >جنس سفارش:</label>
                <select
                    class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 px-6 py-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="sex"
                    wire:model="data.sex"
                >
                    <option value="">--جنس--</option>
                    <option>طلا</option>
                    <option>نقره</option>
                    <option>استیل</option>
                </select>
            </div>
            <div>
                <label
                    class="mb-2 block text-sm text-white"
                    for="name_pluck"
                >اسم پلاک:</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="name_pluck"
                    type="text"
                    wire:model="data.name_pluck"
                >
            </div>
            <div>
                <label
                    class="mb-2 block text-sm text-white"
                    for="model"
                >مدل:</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="model"
                    type="text"
                    wire:model="data.model"
                >
            </div>

            <div>
                <label
                    class="mb-2 block text-sm text-white"
                    for="chain"
                >زنجیر (نوع و جنس):</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="chain"
                    type="text"
                    wire:model="data.chain"
                >
            </div>
            <div>
                <label
                    class="mb-2 block text-sm text-white"
                    for="color"
                >رنگ بندی محصول:</label>
                <select
                    class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 px-6 py-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="color"
                    wire:model="data.color"
                >
                    <option value="">--رنگ--</option>
                    <option>سفید</option>
                    <option>زرد</option>
                </select>
            </div>
            <div>
                <label
                    class="mb-2 block text-sm text-white"
                    for="eticket"
                >اتیکت:</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="eticket"
                    type="text"
                    wire:model="data.eticket"
                >
            </div>
            <div>
                <label
                    class="mb-2 block text-sm text-white"
                    for="fullname"
                >نام گیرنده:</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="fullname"
                    type="text"
                    wire:model="data.fullname"
                >
            </div>
            <div>
                <label
                    class="mb-2 block text-sm text-white"
                    for="phone"
                >شماره تماس گیرنده:</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="phone"
                    type="text"
                    wire:model="data.phone"
                >
            </div>
            <div>
                <label
                    class="mb-2 block text-sm text-white"
                    for="whatsapp"
                >محل سفارش:</label>
                <select
                    class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 px-6 py-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="whatsapp"
                    wire:model="data.whatsapp"
                >
                    <option value="">--محل سفارش--</option>
                    <option>واتس آپ</option>
                    <option>حضوری الماس</option>
                    <option>حضوری همیلا</option>
                    <option>تلگرام</option>
                    <option>سامانه</option>
                    <option>سایت (SnapPay)</option>
                    <option>سایت (DigiPay)</option>
                </select>
            </div>

            <div>
                <label
                    class="mb-2 block text-sm text-white"
                    for="last_status"
                >وضعیت سفارش:</label>
                <select
                    class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 px-6 py-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="last_status"
                    wire:model="data.last_status"
                >
                    <option value="">--وضعیت--</option>
                    <option value="design">در حال طراحی</option>
                    <option value="wait_design">منتظر انتخاب طرح</option>
                    <option value="cut">فایل برش</option>
                    <option
                        value="ready_to_build"
                        style="font-weight:900;color:red"
                    >آماده به ساخت</option>
                    <option value="wait_factory">در حال ساخت</option>
                    <option
                        value="ready"
                        style="font-weight:900;color:blue"
                    >آماده به ارسال</option>
                    <option value="ready-on">درحال ارسال</option>
                    <option value="money">منتظر تسویه مشتری</option>
                    <option value="send">ارسال شد</option>
                    <option value="cancel">کنسل</option>
                </select>
            </div>
            <div>
                <label
                    class="mb-2 block text-sm text-white"
                    for="financial"
                >وضعیت مالی سفارش:</label>
                <select
                    class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 px-6 py-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="financial"
                    wire:model="data.financial"
                >
                    <option value="">--وضعیت مالی--</option>
                    <option value="1">انجام شده</option>
                    <option value="0">انجام نشده</option>
                </select>
            </div>
        </div>
        <div class="mt-3 grid grid-cols-1 gap-3 md:grid-cols-8">

            <div>
                <label
                    class="mb-2 block text-sm text-white"
                    for="postType"
                >نوع ارسال مرسوله:</label>
                <select
                    class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 px-6 py-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="postType"
                    wire:model="data.postType"
                >
                    <option value="">--نوع ارسال--</option>
                    <option>پست</option>
                    {{-- <option value="تیباکس">تیباکس</option> --}}
                    {{-- <option value="پست ویژه">پست ویژه</option> --}}
                    <option>پیک</option>
                    <option>حضوری</option>
                    {{-- <option>حضوری الماس</option> --}}
                    {{-- <option>حضوری همیلا</option> --}}
                    <option>پیک ( تهران و حومه - به عهده مشتری )</option>
                    <option>تحویل حضوری شعبه پونک ( پاساژ همیلا )</option>
                    <option>تحویل حضوری شعبه جنت آباد ( پاساژ الماس )</option>
                </select>
            </div>
            <div>
                <label
                    class="mb-2 block text-sm text-white"
                    for="factor"
                >فاکتورها:</label>
                <select
                    class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 px-6 py-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="factor"
                    wire:model="data.factor"
                >
                    <option value="">--انتخاب کنید--</option>
                    <option value="null">فاکتورهای ناقص</option>
                    <option value="full">فاکتور کامل</option>
                </select>
            </div>
            @if (auth()->user()->level == 'admin')
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="user"
                    >کارشناس مربوطه:</label>
                    <select
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 px-6 py-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="user"
                        wire:model="user"
                    >
                        <option value="">--انتخاب کنید--</option>
                        @foreach (\App\Models\User::select('id', 'level', 'mobile', 'fullname')->whereIn('level', ['admin', 'user'])->get() as $item)
                            <option value="{{ $item->id }}">
                                {{ $item->fullname . ' (' . $item->mobile . ')' }}
                            </option>
                        @endforeach

                    </select>
                </div>
            @endif
        </div>
        <div class="flex flex-row-reverse items-center gap-3 py-3">

            <button
                class="rounded-lg bg-red-500 px-6 py-1 text-white transition-all hover:bg-red-600"
                type="button"
                wire:click="fillter"
            >
                <svg
                    class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                    role="status"
                    aria-hidden="true"
                    wire:loading
                    wire:target="fillter"
                    viewBox="0 0 100 101"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                        fill="#E5E7EB"
                    />
                    <path
                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                        fill="currentColor"
                    />
                </svg>
                <span class="text-sm">اعمال فیلتر</span>
            </button>
            <button
                class="rounded-lg bg-gray-300 px-6 py-1 text-gray-600 transition-all hover:bg-gray-200"
                type="button"
                wire:click="ClearFillter"
            >
                <svg
                    class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                    role="status"
                    aria-hidden="true"
                    wire:loading
                    wire:target="ClearFillter"
                    viewBox="0 0 100 101"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                        fill="#E5E7EB"
                    />
                    <path
                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                        fill="currentColor"
                    />
                </svg>
                <span class="text-sm">حذف فیلترها</span>
            </button>
        </div>
    </div>
</div>
