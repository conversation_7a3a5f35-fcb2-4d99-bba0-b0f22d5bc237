<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Sefaresh;
use App\Models\Setting;
use App\Models\ZinbalTransaction;
use Illuminate\Http\Request;
use PDF;

class OrdersController extends Controller
{
    public function index()
    {
        return view('dashboard.admin.orders.index');
    }

    public function market()
    {
        return view('dashboard.admin.orders.market.index');
    }

    public function create($category)
    {
        $settings = Setting::whereIn('type', ['1', '2'])->get();

        return view('dashboard.admin.orders.create.index', compact('category', 'settings'));
    }

    public function show($orderId)
    {
        $user = auth()->user();
        $isAdmin = $user->level == 'admin';

        $transaction = ZinbalTransaction::where('orderId', $orderId)
            ->where('result', 100)
            ->latest()
            ->first();

        $query = Sefaresh::with('invoice.products.gallery', 'invoice.products.detail', 'invoice.products.parent')
            ->whereId($orderId);

        if (!$isAdmin) {
            $query->where('user_id', $user->id);
        }

        $order = $query->firstOrFail();
        $settings = Setting::whereIn('type', ['1', '2'])->get();

        return view('dashboard.admin.orders.show.index', compact('order', 'settings', 'transaction'));
    }

    public function print($orderId)
    {
        $order = Sefaresh::with('resiver')->whereId($orderId)->firstorfail();

        return view('dashboard.admin.orders.print.address', compact('order'));
    }

    public function prints(Request $req)
    {
        $ids = $req->input('ids', []);
        $orders = Sefaresh::with('resiver')->whereIn('id', $ids)->get();

        return view('dashboard.admin.orders.print.address-list', compact('orders'));
    }

    public function factor_print($orderId)
    {

        $order = Sefaresh::with('resiver')->whereId($orderId)->firstorfail();
        // $pdf = PDF::loadView('dashboard.admin.orders.print.factor', compact('order'), [], ['format' => 'A5-L']);
        // $pdf->autoScriptToLang = true;
        // $pdf->autoLangToFont = true;

        // return $pdf->stream('document.pdf');

        return view('dashboard.admin.orders.print.factor', compact('order'));
    }
}
