<?php

namespace App\Repositories;

use App\Models\Sefaresh;
use App\Models\FactorOrderEmpty;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

/**
 * Repository برای مدیریت عملیات مربوط به سفارشات
 * بهینه‌سازی عملکرد و کاهش تکرار کوئری‌ها
 */
class OrderRepository
{
    /**
     * بارگذاری سفارش با تمام روابط مورد نیاز
     * 
     * @param int $orderId شناسه سفارش
     * @param int|null $userId شناسه کاربر (برای محدود کردن دسترسی)
     * @return Sefaresh
     */
    public function findWithRelations(int $orderId, ?int $userId = null): Sefaresh
    {
        $cacheKey = "order_with_relations_{$orderId}_{$userId}";
        
        return Cache::remember($cacheKey, 300, function () use ($orderId, $userId) {
            $query = Sefaresh::with([
                'invoice.products.gallery',
                'invoice.products.detail',
                'invoice.products.parent',
                'resiver',
                'transaction',
                'financial',
                'user:id,name,email,level',
                'factorTotal'
            ]);

            if ($userId) {
                $query->where('user_id', $userId);
            }

            return $query->findOrFail($orderId);
        });
    }

    /**
     * آپدیت اطلاعات مالی سفارش
     * 
     * @param int $orderId شناسه سفارش
     * @param array $financialData اطلاعات مالی
     * @return bool
     */
    public function updateFinancials(int $orderId, array $financialData): bool
    {
        try {
            // آپدیت سفارش
            $updated = Sefaresh::whereId($orderId)->update([
                'deposit1' => $financialData['deposit1'],
                'deposit2' => $financialData['deposit2'],
                'remaining' => $financialData['remaining'],
                'total_amount' => $financialData['total_amount'],
            ]);

            // آپدیت فاکتور در صورت وجود
            $factor = FactorOrderEmpty::where('order_id', $orderId)->latest()->first();
            if ($factor) {
                $factor->update(['factor_deposit' => $financialData['total_amount']]);
            }

            // پاک کردن کش مربوطه
            $this->clearOrderCache($orderId);

            return $updated > 0;
        } catch (\Exception $e) {
            \Log::error('خطا در آپدیت اطلاعات مالی سفارش', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * آپدیت اطلاعات کلی سفارش
     * 
     * @param int $orderId شناسه سفارش
     * @param array $orderData اطلاعات سفارش
     * @return bool
     */
    public function updateOrder(int $orderId, array $orderData): bool
    {
        try {
            $updated = Sefaresh::whereId($orderId)->update($orderData);
            
            // پاک کردن کش مربوطه
            $this->clearOrderCache($orderId);
            
            return $updated > 0;
        } catch (\Exception $e) {
            \Log::error('خطا در آپدیت سفارش', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * بررسی تغییرات مالی
     * 
     * @param Sefaresh $order سفارش فعلی
     * @param array $newData اطلاعات جدید
     * @return bool
     */
    public function hasFinancialChanges(Sefaresh $order, array $newData): bool
    {
        return $order->deposit1 != $newData['deposit1'] ||
               $order->deposit2 != $newData['deposit2'] ||
               $order->remaining != $newData['remaining'] ||
               $order->total_amount != $newData['total_amount'];
    }

    /**
     * محاسبه اطلاعات مالی
     * 
     * @param float $deposit1 بیعانه اول
     * @param float $deposit2 بیعانه دوم
     * @param float $totalAmount مبلغ کل
     * @return array
     */
    public function calculateFinancials(float $deposit1, float $deposit2, float $totalAmount): array
    {
        $totalDeposits = $deposit1 + $deposit2;
        $remaining = max(0, $totalAmount - $totalDeposits);

        return [
            'deposit1' => $deposit1,
            'deposit2' => $deposit2,
            'total_amount' => $totalDeposits,
            'remaining' => $remaining,
            'total_deposits' => $totalDeposits
        ];
    }

    /**
     * پاک کردن کش مربوط به سفارش
     * 
     * @param int $orderId شناسه سفارش
     * @return void
     */
    public function clearOrderCache(int $orderId): void
    {
        $patterns = [
            "order_with_relations_{$orderId}_*",
            "order_details_{$orderId}",
            "order_financials_{$orderId}"
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }
    }

    /**
     * دریافت آمار سریع سفارش
     * 
     * @param int $orderId شناسه سفارش
     * @return array
     */
    public function getOrderStats(int $orderId): array
    {
        $cacheKey = "order_stats_{$orderId}";
        
        return Cache::remember($cacheKey, 600, function () use ($orderId) {
            $order = Sefaresh::select([
                'id', 'total_amount', 'deposit1', 'deposit2', 
                'remaining', 'last_status', 'created_at'
            ])->find($orderId);

            if (!$order) {
                return [];
            }

            return [
                'total_amount' => $order->total_amount,
                'total_deposits' => $order->deposit1 + $order->deposit2,
                'remaining' => $order->remaining,
                'status' => $order->last_status,
                'created_at' => $order->created_at,
                'completion_percentage' => $order->total_amount > 0 
                    ? round((($order->deposit1 + $order->deposit2) / $order->total_amount) * 100, 2)
                    : 0
            ];
        });
    }

    /**
     * بررسی وضعیت پرداخت سفارش
     * 
     * @param int $orderId شناسه سفارش
     * @return bool
     */
    public function isOrderPaid(int $orderId): bool
    {
        $cacheKey = "order_payment_status_{$orderId}";
        
        return Cache::remember($cacheKey, 300, function () use ($orderId) {
            return Sefaresh::whereId($orderId)
                ->whereHas('transaction', function ($query) {
                    $query->where('result', 100);
                })
                ->exists();
        });
    }

    /**
     * دریافت تاریخچه تغییرات سفارش
     * 
     * @param int $orderId شناسه سفارش
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getOrderHistory(int $orderId)
    {
        $cacheKey = "order_history_{$orderId}";
        
        return Cache::remember($cacheKey, 600, function () use ($orderId) {
            return \App\Models\StatusHistory::where('sefaresh_id', $orderId)
                ->with('user:id,name')
                ->orderBy('created_at', 'desc')
                ->get();
        });
    }

    /**
     * تبدیل مقدار رشته‌ای به عدد
     * 
     * @param mixed $amount مقدار ورودی
     * @return float
     */
    public function parseAmount($amount): float
    {
        if (empty($amount)) {
            return 0;
        }
        return (float) str_replace(',', '', $amount);
    }

    /**
     * فرمت کردن مبلغ برای نمایش
     * 
     * @param float $amount مبلغ
     * @return string
     */
    public function formatAmount(float $amount): string
    {
        return number_format($amount);
    }
}
