<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\OrderCacheService;
use Illuminate\Support\Facades\Cache;

/**
 * دستور پاک کردن کش‌های مربوط به سفارشات
 * برای بهینه‌سازی عملکرد و مدیریت حافظه
 */
class ClearOrderCacheCommand extends Command
{
    /**
     * نام و امضای دستور کنسول
     *
     * @var string
     */
    protected $signature = 'order:clear-cache 
                            {--type=all : نوع کش برای پاک کردن (all, orders, settings, users)}
                            {--order-id= : شناسه سفارش خاص برای پاک کردن کش}
                            {--force : پاک کردن اجباری بدون تأیید}';

    /**
     * توضیحات دستور کنسول
     *
     * @var string
     */
    protected $description = 'پاک کردن کش‌های مربوط به سفارشات برای بهینه‌سازی عملکرد';

    /**
     * سرویس مدیریت کش
     *
     * @var OrderCacheService
     */
    protected $cacheService;

    /**
     * ایجاد نمونه جدید از دستور
     *
     * @param OrderCacheService $cacheService
     */
    public function __construct(OrderCacheService $cacheService)
    {
        parent::__construct();
        $this->cacheService = $cacheService;
    }

    /**
     * اجرای دستور کنسول
     *
     * @return int
     */
    public function handle()
    {
        $type = $this->option('type');
        $orderId = $this->option('order-id');
        $force = $this->option('force');

        // نمایش وضعیت فعلی کش
        $this->displayCacheStatus();

        // تأیید از کاربر در صورت عدم استفاده از --force
        if (!$force && !$this->confirm('آیا مطمئن هستید که می‌خواهید کش‌ها را پاک کنید؟')) {
            $this->info('عملیات لغو شد.');
            return 0;
        }

        // پاک کردن کش بر اساس نوع
        switch ($type) {
            case 'orders':
                $this->clearOrdersCache($orderId);
                break;
            case 'settings':
                $this->clearSettingsCache();
                break;
            case 'users':
                $this->clearUsersCache();
                break;
            case 'all':
            default:
                $this->clearAllCache($orderId);
                break;
        }

        // نمایش وضعیت جدید کش
        $this->info('');
        $this->info('وضعیت کش پس از پاک کردن:');
        $this->displayCacheStatus();

        return 0;
    }

    /**
     * نمایش وضعیت فعلی کش
     */
    protected function displayCacheStatus()
    {
        $this->info('وضعیت فعلی کش:');
        
        $status = $this->cacheService->getCacheStatus();
        $sizes = $this->cacheService->getCacheSize();

        $headers = ['کلید کش', 'وضعیت', 'اندازه (بایت)'];
        $rows = [];

        foreach ($status as $key => $exists) {
            $rows[] = [
                $key,
                $exists ? '✓ موجود' : '✗ موجود نیست',
                $exists ? number_format($sizes[$key] ?? 0) : '-'
            ];
        }

        $this->table($headers, $rows);
    }

    /**
     * پاک کردن کش‌های مربوط به سفارشات
     *
     * @param int|null $orderId
     */
    protected function clearOrdersCache($orderId = null)
    {
        if ($orderId) {
            $this->info("پاک کردن کش سفارش #{$orderId}...");
            $this->cacheService->clearOrderCache($orderId);
            $this->info("کش سفارش #{$orderId} با موفقیت پاک شد.");
        } else {
            $this->info('پاک کردن تمام کش‌های مربوط به سفارشات...');
            
            // در اینجا می‌توان لیست تمام سفارشات را دریافت کرد و کش آن‌ها را پاک کرد
            // یا از الگوی خاصی برای پاک کردن استفاده کرد
            
            $this->warn('برای پاک کردن کش تمام سفارشات، از --type=all استفاده کنید.');
        }
    }

    /**
     * پاک کردن کش‌های مربوط به تنظیمات
     */
    protected function clearSettingsCache()
    {
        $this->info('پاک کردن کش‌های تنظیمات...');
        $this->cacheService->clearSettingsCache();
        $this->info('کش‌های تنظیمات با موفقیت پاک شدند.');
    }

    /**
     * پاک کردن کش‌های مربوط به کاربران
     */
    protected function clearUsersCache()
    {
        $this->info('پاک کردن کش‌های کاربران...');
        Cache::forget('active_users_list');
        $this->info('کش‌های کاربران با موفقیت پاک شدند.');
    }

    /**
     * پاک کردن تمام کش‌ها
     *
     * @param int|null $orderId
     */
    protected function clearAllCache($orderId = null)
    {
        $this->info('پاک کردن تمام کش‌ها...');
        
        // پاک کردن کش عمومی
        $this->cacheService->clearGeneralCache();
        
        // پاک کردن کش تنظیمات
        $this->cacheService->clearSettingsCache();
        
        // پاک کردن کش سفارش خاص در صورت وجود
        if ($orderId) {
            $this->cacheService->clearOrderCache($orderId);
        }
        
        $this->info('تمام کش‌ها با موفقیت پاک شدند.');
    }

    /**
     * گرم کردن کش‌ها پس از پاک کردن
     */
    protected function warmUpCache()
    {
        if ($this->confirm('آیا می‌خواهید کش‌های اصلی را مجدداً گرم کنید؟')) {
            $this->info('گرم کردن کش‌ها...');
            $this->cacheService->warmUpCache();
            $this->info('کش‌ها با موفقیت گرم شدند.');
        }
    }

    /**
     * نمایش آمار کش
     */
    protected function showCacheStats()
    {
        $this->info('آمار کش:');
        
        $stats = [
            'تعداد کلیدهای کش' => count($this->cacheService->getCacheStatus()),
            'کل حجم کش' => array_sum($this->cacheService->getCacheSize()) . ' بایت',
            'کش‌های فعال' => count(array_filter($this->cacheService->getCacheStatus())),
        ];

        foreach ($stats as $key => $value) {
            $this->line("<info>{$key}:</info> {$value}");
        }
    }

    /**
     * بررسی سلامت کش
     */
    protected function checkCacheHealth()
    {
        $this->info('بررسی سلامت کش...');
        
        $status = $this->cacheService->getCacheStatus();
        $healthyCount = count(array_filter($status));
        $totalCount = count($status);
        
        $healthPercentage = $totalCount > 0 ? ($healthyCount / $totalCount) * 100 : 0;
        
        if ($healthPercentage >= 80) {
            $this->info("✓ سلامت کش: {$healthPercentage}% (عالی)");
        } elseif ($healthPercentage >= 60) {
            $this->warn("⚠ سلامت کش: {$healthPercentage}% (متوسط)");
        } else {
            $this->error("✗ سلامت کش: {$healthPercentage}% (ضعیف)");
        }
        
        return $healthPercentage;
    }

    /**
     * پیشنهاد بهینه‌سازی
     */
    protected function suggestOptimizations()
    {
        $healthPercentage = $this->checkCacheHealth();
        
        if ($healthPercentage < 80) {
            $this->info('');
            $this->info('پیشنهادات بهینه‌سازی:');
            $this->line('• اجرای دستور order:clear-cache --type=all برای پاک کردن کش‌های قدیمی');
            $this->line('• بررسی تنظیمات مدت زمان کش در OrderCacheService');
            $this->line('• اجرای دستور cache:clear برای پاک کردن کل کش Laravel');
        }
    }
}
